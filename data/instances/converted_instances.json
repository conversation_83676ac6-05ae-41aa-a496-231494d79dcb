[{"id": 1, "difficulty": 15, "num_stacks": 6, "num_blocks": 15, "start_state": {"Stack1": [12, 13, 2, 5], "Stack2": [10, 8, 11], "Stack3": [4, 6, 1], "Stack4": [15, 7], "Stack5": [9, 14], "Stack6": [3]}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [], "Stack6": []}, "fix_order": ["Stack4", "Stack3", "Stack5", "Stack2", "Stack6", "Stack1"]}, {"id": 2, "difficulty": 15, "num_stacks": 6, "num_blocks": 15, "start_state": {"Stack1": [9], "Stack2": [], "Stack3": [5], "Stack4": [6, 4, 15, 7], "Stack5": [2, 3, 12, 8], "Stack6": [14, 13, 11, 1, 10]}, "goal_state": {"Stack1": [], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": [], "Stack6": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}, "fix_order": ["Stack5", "Stack6", "Stack3", "Stack4", "Stack1", "Stack2"]}, {"start_state": {"Stack1": [12, 13, 6, 2, 1], "Stack2": [3, 5], "Stack3": [8, 4, 7, 10, 9], "Stack4": [11, 15, 14], "Stack5": []}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack4", "Stack5"], "id": 28, "num_stacks": 5, "num_blocks": 15, "difficulty": 15}, {"start_state": {"Stack1": [9, 15, 1, 3], "Stack2": [4, 12], "Stack3": [6, 5, 8, 14], "Stack4": [11, 13, 10, 2, 7], "Stack5": []}, "goal_state": {"Stack1": [15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1], "Stack2": [], "Stack3": [], "Stack4": [], "Stack5": []}, "fix_order": ["Stack1", "Stack2", "Stack3", "Stack4", "Stack5"], "id": 29, "num_stacks": 5, "num_blocks": 15, "difficulty": 15}]