
Running A* with LLM ImprovedBlocksWorldCNN(
  (stack_conv1): Conv2d(22, 64, kernel_size=(5, 1), stride=(1, 1), padding=(2, 0))
  (stack_bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (stack_dropout1): Dropout2d(p=0.1, inplace=False)
  (stack_conv2): Conv2d(64, 128, kernel_size=(3, 1), stride=(1, 1), padding=(1, 0))
  (stack_bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (stack_dropout2): Dropout2d(p=0.1, inplace=False)
  (cross_stack_conv): Conv2d(128, 128, kernel_size=(1, 3), stride=(1, 1), padding=(0, 1))
  (cross_stack_bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (cross_stack_dropout): Dropout2d(p=0.1, inplace=False)
  (multi_scale_conv1): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=(2, 2), dilation=(2, 2))
  (multi_scale_bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (fusion_conv): Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1))
  (fusion_bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (spatial_attention): SpatialAttention(
    (conv1): Conv2d(128, 16, kernel_size=(1, 1), stride=(1, 1))
    (conv2): Conv2d(16, 1, kernel_size=(1, 1), stride=(1, 1))
  )
  (transformer): TransformerEncoder(
    (layers): ModuleList(
      (0-2): 3 x TransformerEncoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
        )
        (linear1): Linear(in_features=128, out_features=512, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=512, out_features=128, bias=True)
        (norm1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
      )
    )
  )
  (classifier_dropout): Dropout(p=0.1, inplace=False)
  (fc_best): Linear(in_features=128, out_features=20, bias=True)
  (fc_worst): Linear(in_features=128, out_features=20, bias=True)
):

[2025-08-20 10:45:14] 运行带限制的A*搜索（max_nodes=50000，指导=ON）
[2025-08-20 10:45:14] ✅ 找到解决方案：[(4, 2), (1, 2), (1, 2), (1, 2), (1, 2), (4, 1), (5, 1), (2, 4), (2, 1), (4, 1), (2, 6), (2, 4), (2, 3), (2, 1), (2, 3), (2, 1), (5, 1), (3, 1), (3, 1), (3, 2), (3, 1), (4, 1), (3, 1), (6, 1), (1, 3), (6, 1), (3, 1), (2, 1)]
[2025-08-20 10:45:14] 搜索节点数：60；路径长度：28；耗时：0.39s
