
Running A* with LLM ImprovedBlocksWorldCNN(
  (stack_conv1): Conv2d(22, 64, kernel_size=(5, 1), stride=(1, 1), padding=(2, 0))
  (stack_bn1): BatchNorm2d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (stack_dropout1): Dropout2d(p=0.1, inplace=False)
  (stack_conv2): Conv2d(64, 128, kernel_size=(3, 1), stride=(1, 1), padding=(1, 0))
  (stack_bn2): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (stack_dropout2): Dropout2d(p=0.1, inplace=False)
  (cross_stack_conv): Conv2d(128, 128, kernel_size=(1, 3), stride=(1, 1), padding=(0, 1))
  (cross_stack_bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (cross_stack_dropout): Dropout2d(p=0.1, inplace=False)
  (multi_scale_conv1): Conv2d(128, 256, kernel_size=(3, 3), stride=(1, 1), padding=(2, 2), dilation=(2, 2))
  (multi_scale_bn1): BatchNorm2d(256, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (fusion_conv): Conv2d(256, 128, kernel_size=(1, 1), stride=(1, 1))
  (fusion_bn): BatchNorm2d(128, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
  (spatial_attention): SpatialAttention(
    (conv1): Conv2d(128, 16, kernel_size=(1, 1), stride=(1, 1))
    (conv2): Conv2d(16, 1, kernel_size=(1, 1), stride=(1, 1))
  )
  (transformer): TransformerEncoder(
    (layers): ModuleList(
      (0-2): 3 x TransformerEncoderLayer(
        (self_attn): MultiheadAttention(
          (out_proj): NonDynamicallyQuantizableLinear(in_features=128, out_features=128, bias=True)
        )
        (linear1): Linear(in_features=128, out_features=512, bias=True)
        (dropout): Dropout(p=0.1, inplace=False)
        (linear2): Linear(in_features=512, out_features=128, bias=True)
        (norm1): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((128,), eps=1e-05, elementwise_affine=True)
        (dropout1): Dropout(p=0.1, inplace=False)
        (dropout2): Dropout(p=0.1, inplace=False)
      )
    )
  )
  (classifier_dropout): Dropout(p=0.1, inplace=False)
  (fc_best): Linear(in_features=128, out_features=20, bias=True)
  (fc_worst): Linear(in_features=128, out_features=20, bias=True)
):

[2025-08-20 10:46:42] 运行带限制的A*搜索（max_nodes=50000，指导=ON）
[2025-08-20 10:46:42] 进度：1000/50000 节点 (elapsed=0.6s)
[2025-08-20 10:46:43] 进度：2000/50000 节点 (elapsed=1.3s)
[2025-08-20 10:46:44] 进度：3000/50000 节点 (elapsed=2.0s)
[2025-08-20 10:46:44] 进度：4000/50000 节点 (elapsed=2.6s)
[2025-08-20 10:46:45] 进度：5000/50000 节点 (elapsed=3.3s)
[2025-08-20 10:46:45] 进度：6000/50000 节点 (elapsed=3.8s)
[2025-08-20 10:46:46] 进度：7000/50000 节点 (elapsed=4.5s)
[2025-08-20 10:46:47] 进度：8000/50000 节点 (elapsed=5.4s)
[2025-08-20 10:46:47] 进度：9000/50000 节点 (elapsed=5.9s)
[2025-08-20 10:46:48] 进度：10000/50000 节点 (elapsed=6.8s)
[2025-08-20 10:46:49] 进度：11000/50000 节点 (elapsed=7.3s)
[2025-08-20 10:46:49] 进度：12000/50000 节点 (elapsed=7.8s)
[2025-08-20 10:46:50] 进度：13000/50000 节点 (elapsed=8.9s)
[2025-08-20 10:46:51] 进度：14000/50000 节点 (elapsed=9.4s)
[2025-08-20 10:46:51] 进度：15000/50000 节点 (elapsed=9.9s)
[2025-08-20 10:46:53] 进度：16000/50000 节点 (elapsed=11.0s)
[2025-08-20 10:46:53] 进度：17000/50000 节点 (elapsed=11.5s)
[2025-08-20 10:46:53] 进度：18000/50000 节点 (elapsed=11.9s)
[2025-08-20 10:46:54] 进度：19000/50000 节点 (elapsed=12.4s)
[2025-08-20 10:46:55] 进度：20000/50000 节点 (elapsed=13.7s)
[2025-08-20 10:46:56] 进度：21000/50000 节点 (elapsed=14.2s)
[2025-08-20 10:46:56] 进度：22000/50000 节点 (elapsed=14.6s)
[2025-08-20 10:46:57] 进度：23000/50000 节点 (elapsed=15.1s)
[2025-08-20 10:46:57] 进度：24000/50000 节点 (elapsed=15.6s)
[2025-08-20 10:46:59] 进度：25000/50000 节点 (elapsed=17.2s)
[2025-08-20 10:46:59] 进度：26000/50000 节点 (elapsed=17.7s)
[2025-08-20 10:47:00] 进度：27000/50000 节点 (elapsed=18.2s)
[2025-08-20 10:47:00] 进度：28000/50000 节点 (elapsed=18.7s)
[2025-08-20 10:47:01] 进度：29000/50000 节点 (elapsed=19.2s)
[2025-08-20 10:47:01] 进度：30000/50000 节点 (elapsed=19.7s)
[2025-08-20 10:47:03] 进度：31000/50000 节点 (elapsed=21.4s)
[2025-08-20 10:47:03] 进度：32000/50000 节点 (elapsed=21.8s)
[2025-08-20 10:47:04] 进度：33000/50000 节点 (elapsed=22.3s)
[2025-08-20 10:47:04] 进度：34000/50000 节点 (elapsed=22.7s)
[2025-08-20 10:47:05] 进度：35000/50000 节点 (elapsed=23.1s)
[2025-08-20 10:47:05] 进度：36000/50000 节点 (elapsed=23.6s)
[2025-08-20 10:47:06] 进度：37000/50000 节点 (elapsed=24.0s)
[2025-08-20 10:47:08] 进度：38000/50000 节点 (elapsed=26.1s)
[2025-08-20 10:47:08] 进度：39000/50000 节点 (elapsed=26.5s)
[2025-08-20 10:47:09] 进度：40000/50000 节点 (elapsed=27.0s)
[2025-08-20 10:47:09] 进度：41000/50000 节点 (elapsed=27.5s)
[2025-08-20 10:47:10] 进度：42000/50000 节点 (elapsed=27.9s)
[2025-08-20 10:47:10] 进度：43000/50000 节点 (elapsed=28.4s)
[2025-08-20 10:47:10] 进度：44000/50000 节点 (elapsed=28.9s)
[2025-08-20 10:47:11] 进度：45000/50000 节点 (elapsed=29.4s)
[2025-08-20 10:47:11] 进度：46000/50000 节点 (elapsed=29.9s)
[2025-08-20 10:47:14] 进度：47000/50000 节点 (elapsed=32.5s)
[2025-08-20 10:47:15] 进度：48000/50000 节点 (elapsed=33.0s)
[2025-08-20 10:47:15] 进度：49000/50000 节点 (elapsed=33.4s)
[2025-08-20 10:47:15] 进度：50000/50000 节点 (elapsed=33.9s)
[2025-08-20 10:47:15] ❌ 达到最大节点限制（50000），未找到解
[2025-08-20 10:47:15] 搜索节点数：50000；耗时：33.91s
