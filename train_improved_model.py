import os
import argparse
import json
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset, random_split

from improved_cnn_model import ImprovedBlocksWorldCNN


def load_dataset(prefix: str):
    X = np.load(f"{prefix}_matrices.npy")  # (N, 22, 15, 6)
    Y = np.load(f"{prefix}_labels.npy")    # (N, 2)
    return X, Y


def make_dataloaders(X, Y, batch_size=32, val_ratio=0.2, test_ratio=0.1, shuffle=True, seed=42):
    torch.manual_seed(seed)
    N = X.shape[0]
    X_t = torch.tensor(X, dtype=torch.float32)
    Y_t = torch.tensor(Y, dtype=torch.long)
    ds = TensorDataset(X_t, Y_t)

    test_size = int(N * test_ratio)
    val_size = int(N * val_ratio)
    train_size = N - val_size - test_size
    train_ds, val_ds, test_ds = random_split(ds, [train_size, val_size, test_size])

    train_loader = DataLoader(train_ds, batch_size=batch_size, shuffle=shuffle)
    val_loader = DataLoader(val_ds, batch_size=batch_size, shuffle=False)
    test_loader = DataLoader(test_ds, batch_size=batch_size, shuffle=False)
    return train_loader, val_loader, test_loader


def masked_ce_loss(logits, targets, label_smoothing=0.0):
    # logits: (M, C); targets: (M,)
    if label_smoothing > 0:
        loss_fn = nn.CrossEntropyLoss(label_smoothing=label_smoothing)
    else:
        loss_fn = nn.CrossEntropyLoss()
    return loss_fn(logits, targets)


def train(model, train_loader, val_loader, device, epochs=50, lr=1e-3, weight_decay=1e-4,
          label_smoothing=0.0, worst_loss_coef=0.5, patience=10, save_path='best_model.pth',
          milestones=None, gamma=0.5):
    model.to(device)
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    scheduler = None
    if milestones:
        try:
            scheduler = torch.optim.lr_scheduler.MultiStepLR(optimizer, milestones=milestones, gamma=gamma)
            print('Using MultiStepLR with milestones=', milestones, 'gamma=', gamma)
        except Exception as e:
            print('Failed to create scheduler:', e)
            scheduler = None

    best_val_loss = float('inf')
    best_state = None
    no_improve = 0

    for epoch in range(1, epochs + 1):
        model.train()
        total_loss = 0.0
        batches = 0
        for xb, yb in train_loader:
            xb = xb.to(device)
            yb = yb.to(device)
            optimizer.zero_grad()
            best_logits, worst_logits = model(xb)

            best_mask = yb[:, 0] != -1
            worst_mask = yb[:, 1] != -1

            loss = 0.0
            if best_mask.any():
                loss_best = masked_ce_loss(best_logits[best_mask], yb[best_mask, 0], label_smoothing)
                loss = loss + loss_best
            if worst_mask.any() and worst_loss_coef > 0:
                loss_worst = masked_ce_loss(worst_logits[worst_mask], yb[worst_mask, 1], label_smoothing)
                loss = loss + worst_loss_coef * loss_worst

            if loss != 0:
                loss.backward()
                optimizer.step()
                total_loss += float(loss.item())
                batches += 1
        train_loss = total_loss / max(1, batches)

        # validation
        model.eval()
        val_total = 0.0
        val_batches = 0
        correct_best = 0
        correct_worst = 0
        total_best = 0
        total_worst = 0
        with torch.no_grad():
            for xb, yb in val_loader:
                xb = xb.to(device)
                yb = yb.to(device)
                best_logits, worst_logits = model(xb)
                best_mask = yb[:, 0] != -1
                worst_mask = yb[:, 1] != -1
                vloss = 0.0
                if best_mask.any():
                    v_best = masked_ce_loss(best_logits[best_mask], yb[best_mask, 0], label_smoothing)
                    vloss += float(v_best.item())
                    preds = best_logits[best_mask].argmax(dim=1)
                    correct_best += int((preds == yb[best_mask, 0]).sum().item())
                    total_best += int(best_mask.sum().item())
                if worst_mask.any():
                    v_worst = masked_ce_loss(worst_logits[worst_mask], yb[worst_mask, 1], label_smoothing)
                    vloss += float(v_worst.item())
                    preds = worst_logits[worst_mask].argmax(dim=1)
                    correct_worst += int((preds == yb[worst_mask, 1]).sum().item())
                    total_worst += int(worst_mask.sum().item())
                if vloss != 0:
                    val_total += vloss
                    val_batches += 1
        val_loss = val_total / max(1, val_batches)
        acc_best = correct_best / total_best if total_best > 0 else 0.0
        acc_worst = correct_worst / total_worst if total_worst > 0 else 0.0

        # Step LR scheduler
        if scheduler is not None:
            scheduler.step()

        print(f"Epoch {epoch:03d}: train_loss={train_loss:.4f} val_loss={val_loss:.4f} val_acc_best={acc_best:.3f} val_acc_worst={acc_worst:.3f}")

        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_state = {k: v.cpu() for k, v in model.state_dict().items()}
            no_improve = 0
        else:
            no_improve += 1
            if no_improve >= patience:
                print(f"Early stopping at epoch {epoch}")
                break

    if best_state is not None:
        torch.save(best_state, save_path)
        print(f"Saved best model to {save_path} (val_loss={best_val_loss:.4f})")

    return best_val_loss


def test(model, test_loader, device):
    model.to(device)
    model.eval()
    correct_best = 0
    correct_worst = 0
    total_best = 0
    total_worst = 0
    with torch.no_grad():
        for xb, yb in test_loader:
            xb = xb.to(device)
            yb = yb.to(device)
            best_logits, worst_logits = model(xb)
            best_mask = yb[:, 0] != -1
            worst_mask = yb[:, 1] != -1
            if best_mask.any():
                preds = best_logits[best_mask].argmax(dim=1)
                correct_best += int((preds == yb[best_mask, 0]).sum().item())
                total_best += int(best_mask.sum().item())
            if worst_mask.any():
                preds = worst_logits[worst_mask].argmax(dim=1)
                correct_worst += int((preds == yb[worst_mask, 1]).sum().item())
                total_worst += int(worst_mask.sum().item())
    acc_best = correct_best / total_best if total_best > 0 else 0.0
    acc_worst = correct_worst / total_worst if total_worst > 0 else 0.0
    print(f"Test acc - best: {acc_best:.3f}, worst: {acc_worst:.3f}")


def main():
    parser = argparse.ArgumentParser(description='Train ImprovedBlocksWorldCNN on built dataset')
    parser.add_argument('--data_prefix', type=str, default='dataset_from_log_train', help='Prefix of dataset npy files')
    parser.add_argument('--epochs', type=int, default=80)
    parser.add_argument('--batch_size', type=int, default=16)
    parser.add_argument('--lr', type=float, default=3e-4)
    parser.add_argument('--weight_decay', type=float, default=1e-4)
    parser.add_argument('--val_ratio', type=float, default=0.2)
    parser.add_argument('--test_ratio', type=float, default=0.1)
    parser.add_argument('--label_smoothing', type=float, default=0.05)
    parser.add_argument('--worst_loss_coef', type=float, default=0.10)
    parser.add_argument('--patience', type=int, default=40)
    parser.add_argument('--embed_dim', type=int, default=128)
    parser.add_argument('--n_heads', type=int, default=8)
    parser.add_argument('--n_layers', type=int, default=22)
    parser.add_argument('--n_blocks', type=int, default=15)
    parser.add_argument('--n_stacks', type=int, default=6)  # include buffer
    parser.add_argument('--n_classes', type=int, default=20)
    parser.add_argument('--dropout', type=float, default=0.1)
    parser.add_argument('--lr_milestones', type=str, default='30,60')
    parser.add_argument('--lr_gamma', type=float, default=0.5)
    parser.add_argument('--model_out', type=str, default='improved_model_best.pth')

    args = parser.parse_args()

    X, Y = load_dataset(args.data_prefix)
    train_loader, val_loader, test_loader = make_dataloaders(
        X, Y, batch_size=args.batch_size, val_ratio=args.val_ratio, test_ratio=args.test_ratio
    )

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print('Using device:', device)

    # Parse milestones
    milestones = []
    if args.lr_milestones:
        try:
            milestones = [int(x) for x in args.lr_milestones.split(',') if x.strip()!='']
            milestones.sort()
        except Exception as e:
            print('Failed to parse lr_milestones, ignoring:', e)
            milestones = []

    model = ImprovedBlocksWorldCNN(
        n_layers=args.n_layers,
        n_blocks=args.n_blocks,
        n_stacks=args.n_stacks,
        embed_dim=args.embed_dim,
        n_heads=args.n_heads,
        n_transformer_layers=3,
        n_classes=args.n_classes,
        dropout_rate=args.dropout,
    )

    train(
        model,
        train_loader,
        val_loader,
        device,
        epochs=args.epochs,
        lr=args.lr,
        weight_decay=args.weight_decay,
        label_smoothing=args.label_smoothing,
        worst_loss_coef=args.worst_loss_coef,
        patience=args.patience,
        save_path=args.model_out,
        milestones=milestones,
        gamma=args.lr_gamma,
    )

    # Load best and test
    state = torch.load(args.model_out, map_location='cpu')
    model.load_state_dict(state)
    test(model, test_loader, device)


if __name__ == '__main__':
    main()
