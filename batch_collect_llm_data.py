#!/usr/bin/env python3
"""
批量处理随机算例，收集LLM指导的A*搜索数据
基于simple_test.py的模式，自动化执行所有生成的问题
"""

import os
import sys
import time
import json
import re
import ast
from typing import Dict, List

# --- 环境设置 ---
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
if SCRIPT_DIR not in sys.path:
    sys.path.append(SCRIPT_DIR)

# --- 模块导入 ---
try:
    from limited_astar_guidance_local import LimitedGraphPlanningBlocksWorld as GraphPlanningBlocksWorld
    print("✅ 成功导入本地 LimitedGraphPlanningBlocksWorld")
except ImportError as e:
    print(f"❌ 导入A*规划器失败: {e}")
    sys.exit(1)

try:
    from llm_guidance import SimpleLLMGuidance
    LLM_GUIDANCE_AVAILABLE = True
    print("✅ 成功导入 SimpleLLMGuidance (local)")
except ImportError as e:
    LLM_GUIDANCE_AVAILABLE = False
    print(f"❌ 导入LLM指导模块失败: {e}")
    sys.exit(1)

def load_random_instances(json_file: str) -> List[Dict]:
    """加载随机算例"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            instances = json.load(f)
        print(f"✅ 成功加载 {len(instances)} 个随机算例")
        return instances
    except Exception as e:
        print(f"❌ 加载算例失败: {e}")
        return []

def run_llm_guided_instance(instance: Dict, output_dir: str) -> Dict:
    """
    为单个算例运行LLM指导的A*搜索
    
    Args:
        instance: 算例数据
        output_dir: 输出目录
        
    Returns:
        结果统计字典
    """
    instance_id = instance['id']
    start_state = instance['start_state']
    goal_state = instance['goal_state']
    fix_order = instance['fix_order']
    difficulty = instance['difficulty']
    
    print(f"\n🔍 处理算例 {instance_id} (难度: {difficulty})")
    
    # 创建日志文件
    log_file = os.path.join(output_dir, f"instance_{instance_id:03d}_llm_log.txt")
    
    result = {
        'instance_id': instance_id,
        'difficulty': difficulty,
        'success': False,
        'solution_length': 0,
        'nodes_expanded': 0,
        'duration': 0.0,
        'error': None
    }
    
    try:
        # 创建LLM指导实例
        llm_instance = SimpleLLMGuidance()
        
        # 创建规划器
        planner = GraphPlanningBlocksWorld(
            start_state=start_state,
            goal_state=goal_state,
            fix_order=fix_order,
            log_file=log_file
        )
        
        print(f"  🚀 开始LLM指导的A*搜索...")
        start_time = time.time()
        
        # 运行搜索
        solution, nodes_count = planner.a_star_search(llm=llm_instance)
        
        duration = time.time() - start_time
        
        # 记录结果
        result['duration'] = duration
        result['nodes_expanded'] = nodes_count
        
        if solution:
            result['success'] = True
            result['solution_length'] = len(solution)
            print(f"  ✅ 找到解决方案! (路径长度: {len(solution)}, 节点: {nodes_count}, 耗时: {duration:.2f}s)")
        else:
            print(f"  ❌ 未找到解决方案 (节点: {nodes_count}, 耗时: {duration:.2f}s)")
        
        # 在日志文件末尾添加结果摘要
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"算例 {instance_id} 结果摘要:\n")
            f.write(f"  - 难度: {difficulty}\n")
            f.write(f"  - 成功: {'是' if result['success'] else '否'}\n")
            f.write(f"  - 解决方案长度: {result['solution_length']}\n")
            f.write(f"  - 扩展节点数: {result['nodes_expanded']}\n")
            f.write(f"  - 耗时: {result['duration']:.4f}秒\n")
            f.write(f"{'='*60}\n")
        
    except Exception as e:
        result['error'] = str(e)
        print(f"  ❌ 处理失败: {e}")
        
        # 记录错误到日志文件
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*60}\n")
            f.write(f"算例 {instance_id} 执行失败:\n")
            f.write(f"错误: {e}\n")
            f.write(f"{'='*60}\n")
    
    return result

def save_batch_results(results: List[Dict], output_file: str):
    """保存批处理结果"""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print(f"✅ 批处理结果已保存到: {output_file}")

def analyze_batch_results(results: List[Dict]):
    """分析批处理结果"""
    if not results:
        print("❌ 没有结果可分析")
        return
    
    total_instances = len(results)
    successful_instances = sum(1 for r in results if r['success'])
    failed_instances = total_instances - successful_instances
    
    total_duration = sum(r['duration'] for r in results)
    total_nodes = sum(r['nodes_expanded'] for r in results)
    
    successful_results = [r for r in results if r['success']]
    if successful_results:
        avg_solution_length = sum(r['solution_length'] for r in successful_results) / len(successful_results)
        avg_nodes_success = sum(r['nodes_expanded'] for r in successful_results) / len(successful_results)
    else:
        avg_solution_length = 0
        avg_nodes_success = 0
    
    print(f"\n📊 批处理结果分析:")
    print(f"  - 总算例数: {total_instances}")
    print(f"  - 成功算例: {successful_instances} ({successful_instances/total_instances*100:.1f}%)")
    print(f"  - 失败算例: {failed_instances} ({failed_instances/total_instances*100:.1f}%)")
    print(f"  - 总耗时: {total_duration:.2f}秒")
    print(f"  - 平均每算例: {total_duration/total_instances:.2f}秒")
    print(f"  - 总扩展节点: {total_nodes}")
    print(f"  - 平均节点数: {total_nodes/total_instances:.0f}")
    
    if successful_results:
        print(f"  - 成功算例平均解长度: {avg_solution_length:.1f}")
        print(f"  - 成功算例平均节点数: {avg_nodes_success:.0f}")
    
    # 按难度分析
    difficulty_stats = {}
    for result in results:
        diff = result['difficulty']
        if diff not in difficulty_stats:
            difficulty_stats[diff] = {'total': 0, 'success': 0}
        difficulty_stats[diff]['total'] += 1
        if result['success']:
            difficulty_stats[diff]['success'] += 1
    
    print(f"\n  按难度分析:")
    for diff in sorted(difficulty_stats.keys()):
        stats = difficulty_stats[diff]
        success_rate = stats['success'] / stats['total'] * 100
        print(f"    难度 {diff}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)")

def main():
    """主函数"""
    import argparse
    parser = argparse.ArgumentParser(description="批量收集LLM指导的A*搜索数据")
    parser.add_argument('--instances_file', default=os.path.join(SCRIPT_DIR, 'random_instances', 'random_instances.json'))
    parser.add_argument('--output_dir', default=os.path.join(SCRIPT_DIR, 'llm_guidance_logs'))
    parser.add_argument('--results_file', default=os.path.join(SCRIPT_DIR, 'batch_llm_results.json'))
    parser.add_argument('--max_instances', type=int, default=50)
    parser.add_argument('--max_nodes', type=int, default=500, help='限制每个算例的最大扩展节点数以控制API调用量')
    args = parser.parse_args()

    print("=" * 80)
    print("批量收集LLM指导的A*搜索数据")
    print("=" * 80)

    print("配置参数:")
    print(f"  - 算例文件: {args.instances_file}")
    print(f"  - 日志目录: {args.output_dir}")
    print(f"  - 结果文件: {args.results_file}")
    print(f"  - 最大处理数量: {args.max_instances}")
    print(f"  - 每算例最大节点数: {args.max_nodes}")

    if not LLM_GUIDANCE_AVAILABLE:
        print("❌ LLM指导模块不可用，无法继续")
        return

    if not os.path.exists(args.instances_file):
        print(f"❌ 算例文件不存在: {args.instances_file}")
        return

    os.makedirs(args.output_dir, exist_ok=True)

    instances = load_random_instances(args.instances_file)
    if not instances:
        return

    if len(instances) > args.max_instances:
        instances = instances[:args.max_instances]
        print(f"⚠️ 限制处理前 {args.max_instances} 个算例")

    print(f"\n🚀 开始批量处理 {len(instances)} 个算例...")
    results = []
    start_time = time.time()

    for i, instance in enumerate(instances):
        print(f"\n进度: {i+1}/{len(instances)}")

        # 将 max_nodes 传入规划器：通过闭包变量替换 run_llm_guided_instance 内部 new
        # 这里直接在实例字典中放入一个提示字段供日志识别（非必需）
        global GraphPlanningBlocksWorld
        old_cls = GraphPlanningBlocksWorld
        class _PatchedPlanner(old_cls):
            def __init__(self, *p, **kw):
                kw.setdefault('max_nodes', args.max_nodes)
                super().__init__(*p, **kw)
        GraphPlanningBlocksWorld = _PatchedPlanner

        result = run_llm_guided_instance(instance, args.output_dir)
        results.append(result)
        GraphPlanningBlocksWorld = old_cls

        if (i + 1) % 10 == 0:
            temp_results_file = f"temp_{os.path.basename(args.results_file)}"
            save_batch_results(results, os.path.join(args.output_dir, temp_results_file))
            print(f"  💾 已保存中间结果")

    total_time = time.time() - start_time
    save_batch_results(results, args.results_file)
    analyze_batch_results(results)

    print(f"\n✅ 批量处理完成!")
    print(f"  - 总耗时: {total_time:.2f}秒")
    print(f"  - 日志文件保存在: {args.output_dir}/")
    print(f"  - 结果摘要保存在: {args.results_file}")
    print(f"\n下一步: 使用收集的日志数据重新训练CNN-Transformer模型")

if __name__ == "__main__":
    main()