# Blocks World CNN+Transformer Guided A* Search (Improved Model)

## 项目概述

本项目实现了一个结合深度学习模型（CNN+Transformer）和启发式搜索算法（A*）的智能规划系统，用于解决Blocks World问题。通过从LLM生成的引导日志中学习，训练神经网络模型来预测最佳和最差动作，并将其集成到A*搜索中以提高搜索效率。

## 功能框架（更新版）

```mermaid
flowchart LR
    A[LLM 日志/随机算例] -->|build_dataset.py / generate_random_instances.py| B[(训练数据集\n(npy/json/metadata))]
    subgraph Model
      C[improved_cnn_model.py\n(Encoder+CNN+Transformer)]
      D[train_improved_model.py\n(训练/验证/早停/调度)]
      C --> D
    end
    B --> D
    D --> E[模型权重\n(.pth)]

    subgraph GuidedSearch
      F[test_guided_astar_final.py]
      G[limited_astar_guidance_local.py\n(带节点限制的A*)]
      H[render_solution_gif.py\n(可视化)]
    end

    E --> F
    F <-->|模型引导+通用规则| G
    F -->|导出JSON| I[data/solutions/*.json]
    I --> H
```

### 核心组件

1. **数据处理管道** (`build_dataset.py`、`generate_random_instances.py`、`batch_collect_llm_data.py`、`convert_instance_to_json.py`)

   - 解析Node风格的LLM引导日志
   - 提取原始训练样本
   - 编码为模型输入格式（22×15×6张量）
   - 生成训练数据集（.npy文件）
   - **实例格式转换**：将.txt格式的问题实例转换为LLM guidance可用的JSON格式
2. **深度学习模型** (`improved_cnn_model.py`)

   - CNN特征提取器：处理空间结构信息（见《MODEL_DESIGN.md》中“深入：CNN 设计原理与本研究的契合”）
   - Transformer编码器：捕获块之间的关系
   - 多任务输出：同时预测最佳和最差动作
   - 内置BlocksWorldEncoder：状态编码器
3. **模型训练** (`train_improved_model.py`)

   - 支持多种超参数配置
   - 早停机制防止过拟合
   - 学习率调度优化训练
   - 标签平滑正则化
4. **引导搜索** (`test_guided_astar_final.py`、`limited_astar_guidance_local.py`、`render_solution_gif.py`)

   - 模型引导的A*搜索
   - 通用规则增强
   - 节点扩展限制
   - 成本塑形机制

## 问题发现与解决过程

### 1. 数据解析问题

**发现的问题：**

- 原始的`extract_raw_training_data.py`脚本无法从日志中提取任何样本
- 节点和行的解析逻辑不匹配实际日志格式
- 统计打印时出现除零错误

**解决方案：**

- 重写解析逻辑，改为逐行解析
- 按Node ID聚合数据
- 改进正则表达式匹配
- 添加零除错误处理
- 成功从日志中提取109个训练样本

### 2. 数据格式不一致

**发现的问题：**

- `convert_data.py`和`extract_raw_training_data.py`处理不同格式的日志
- 两个脚本的输出格式不兼容
- 需要两步处理流程，容易出错

**解决方案：**

- 扩展`convert_data.py`支持JSON格式输入
- 创建统一的`build_dataset.py`一步完成全部处理
- 将编码器类整合到模型脚本中
- 建立清晰的数据流管道

### 3. 模型性能瓶颈

**发现的问题：**

- 初始模型训练后测试准确率仅约30%（最佳动作）和50%（最差动作）
- 验证损失在3.4左右停滞
- 模型在某些关键状态下决策质量差

**尝试的优化：**

- 减小模型容量避免过拟合
- 增强dropout（0.3→0.5）
- 降低学习率（1e-3→5e-4）
- 添加学习率调度（CosineAnnealingLR）
- 增加早停耐心（10→20）
- 引入标签平滑（0.1）

**结果分析：**

- 改进有限，主要瓶颈在于训练数据量（仅109个样本）
- 多类别分类任务（22个类别）需要更多数据
- 某些关键状态（如空栈重建）在训练数据中罕见

### 4. 搜索集成挑战

**发现的问题：**

- 模型引导的A*搜索容易陷入局部循环
- 在清空栈后无法有效重建
- 搜索空间爆炸导致节点限制耗尽

**解决方案：**

- 实现节点扩展限制（max_nodes参数）
- 添加成本塑形机制（alpha权重平衡）
- 支持top-k动作选择
- 集成访问状态记录避免重复

### 5. 关键洞察：通用规则的必要性

**问题分析：**
通过深入分析搜索日志，发现了一个关键问题：

- 模型在Stack1被清空后，不知道如何重建
- 训练数据中Stack1为空的状态极少
- 模型倾向于继续将块移出Stack1，而不是移入

**创新解决方案：通用规则增强**

开发了一个领域特定的通用规则：

```python
def check_universal_rule(self, state, legal_actions):
    """
    通用规则：如果某个目标栈需要的下一个块恰好在另一个栈顶部可用，
    且该目标栈当前为空或与目标状态匹配，则该移动是最优的。
    """
```

**规则设计原理：**

1. 检查每个栈是否与目标状态兼容（空栈或底部块匹配）
2. 确定该栈需要的下一个块
3. 如果该块在其他栈顶可用，立即选择该动作
4. 否则回退到模型预测

**实施过程中的调试：**

- 初始实现使用整数键导致规则不触发
- 修正为字符串键（'Stack1', 'Stack2'等）
- 添加详细日志验证规则触发
- 通过对比测试验证改进效果

## 关键技术创新

### 1. 混合智能系统

- 结合数据驱动的深度学习和知识驱动的规则
- 在模型不确定时使用确定性规则
- 保持系统的可解释性和可靠性

### 2. 端到端数据管道

- 从原始日志到训练数据的自动化处理
- 统一的编码器确保一致性
- 支持多种日志格式

### 3. 自适应搜索策略

- 动态成本塑形
- Top-k动作过滤
- 节点扩展限制
- 访问状态记忆

## 实验结果

### 模型训练结果（本次）

- 使用数据：data/datasets/dataset_from_logs
- 最佳验证损失：3.1467（第 56 个 epoch）
- 测试准确率：best 38.5%，worst 69.2%

### 搜索性能对比

- 纯模型引导：经常耗尽节点限制
- 通用规则增强：显著减少搜索节点
- 解决方案质量：通用规则版本找到更优解

#### 三种设定对比（instance_28, max_nodes=50k, alpha=0.5）

- 神经网络指导 + 通用规则
  - 结果：找到解
  - 路径长度：34
  - 扩展节点：122
  - 解：data/solutions/instance_28_solution_rule.json
  - GIF：data/gifs/instance_28_solution_rule.gif
- 神经网络指导（无规则）
  - 结果：找到解
  - 路径长度：36
  - 扩展节点：144
  - 解：data/solutions/instance_28_solution_no_rule.json
  - GIF：data/gifs/instance_28_solution_no_rule.gif
- 仅通用规则（无NN评分）
  - 结果：未收敛（达到 50000 节点上限）
  - 扩展节点：50000
  - 日志：data/logs/search/instance_28_rule_only.txt

结论：在该实例上，“规则 + 学习”明显优于“仅规则”；且对“命中规则”的最优/最差动作施加更强的正/负向偏置，对效率提升显著。

## 反思与启发

### 1. 数据质量vs数据量

- 109个样本对于22类分类任务太少
- 数据不平衡导致某些关键状态学习不足
- 未来需要数据增强或主动学习策略

### 2. 混合方法的价值

- 纯深度学习在数据稀缺时表现有限
- 领域知识可以显著提升系统性能
- 规则和学习的结合是实用AI系统的关键

### 3. 迭代开发的重要性

- 从简单脚本到完整系统的渐进式开发
- 每个问题的解决带来新的洞察
- 保持代码整洁和模块化便于迭代

### 4. 调试和分析工具

- 详细的日志对于理解系统行为至关重要
- 可视化工具帮助发现模式和问题
- 对比实验验证改进效果

## 使用指南（更新）

### 1. 数据准备（data 目录）

```bash
# 1) 生成随机算例 → data/instances
python generate_random_instances.py --output_dir data/instances

# 2) 批量运行 LLM 引导 A* 收集日志 → data/logs/llm
python batch_collect_llm_data.py \
  --instances_file data/instances/random_instances.json \
  --output_dir data/logs/llm \
  --results_file data/logs/llm/batch_results.json

# 3) 从日志构建训练数据（Node 风格）→ data/datasets
python build_dataset.py \
  --input_dir data/logs/llm \
  --output_prefix data/datasets/dataset_from_logs \
  --save_raw_json
```

### 2. 模型训练

```bash
# 使用 data/datasets 训练，模型输出到 data/models
python train_improved_model.py \
  --data_prefix data/datasets/dataset_from_logs \
  --model_out data/models/improved_model_best.pth

# 可选超参
python train_improved_model.py \
  --data_prefix data/datasets/dataset_from_logs \
  --epochs 80 --batch_size 16 \
  --lr 3e-4 --weight_decay 1e-4 \
  --label_smoothing 0.05 --worst_loss_coef 0.10 \
  --patience 40 --embed_dim 128 --n_heads 8 --dropout 0.1 \
  --model_out data/models/improved_model_best.pth
```

### 3. 测试引导搜索（保存解与日志到 data）

```bash
# NN+规则（推荐）
python test_guided_astar_final.py \
  --instance data/instances/instance_28.txt \
  --model data/models/improved_model_best.pth \
  --max_nodes 50000 \
  --use_rule \
  --alpha 0.5 \
  --solution_out data/solutions/instance_28_solution_rule.json \
  --log data/logs/search/instance_28_guided_rule.txt

# NN+规则 + 动态权重（一致性感知，含算法侧加速）
python test_guided_astar_final.py \
  --instance data/instances/instance_28.txt \
  --model data/models/improved_model_best.pth \
  --max_nodes 50000 \
  --use_rule \
  --alpha 0.5 \
  --use_dynamic_weights \
  --solution_out data/solutions/instance_28_solution_rule_dyn_cached.json \
  --log data/logs/search/instance_28_guided_rule_dyn_cached.txt

# NN 无规则
python test_guided_astar_final.py \
  --instance data/instances/instance_28.txt \
  --model data/models/improved_model_best.pth \
  --max_nodes 50000 \
  --alpha 0.5 \
  --no_rule \
  --solution_out data/solutions/instance_28_solution_no_rule.json \
  --log data/logs/search/instance_28_no_rule.txt

# 仅规则（无NN评分）
python test_guided_astar_final.py \
  --instance data/instances/instance_28.txt \
  --model data/models/improved_model_best.pth \
  --max_nodes 50000 \
  --use_rule --rule_only \
  --alpha 0.5 \
  --solution_out data/solutions/instance_28_solution_rule_only.json \
  --log data/logs/search/instance_28_rule_only.txt
```

### Quick compare（两条常用对比命令）

```bash
# NN 无规则（基线）
python test_guided_astar_final.py \
  --instance data/instances/instance_28.txt \
  --model data/models/improved_model_best.pth \
  --max_nodes 50000 --alpha 0.5 \
  --no_rule \
  --solution_out data/solutions/instance_28_solution_no_rule.json \
  --log data/logs/search/instance_28_no_rule.txt

# 仅通用规则（无NN评分）
python test_guided_astar_final.py \
  --instance data/instances/instance_28.txt \
  --model data/models/improved_model_best.pth \
  --max_nodes 50000 --alpha 0.5 \
  --use_rule --rule_only \
  --solution_out data/solutions/instance_28_solution_rule_only.json \
  --log data/logs/search/instance_28_rule_only.txt
```

### 4. 渲染解的动画（GIF）

当`test_guided_astar_final.py`找到解后，可以通过`--solution_out`导出解的 JSON，并用`render_solution_gif.py`渲染动画：

### 5. 对照运行示例

- 仅通用规则（无神经网络指导）

```bash
python test_guided_astar_final.py \
  --instance instance_28.txt \
  --max_nodes 50000 \
  --use_rule \
  --rule_only \
  --alpha 0.5 \
  --log rule_only_n50k.txt
```

- 完全无指导（既不使用NN，也不使用通用规则）

```bash
python test_guided_astar_final.py \
  --instance instance_28.txt \
  --max_nodes 50000 \
  --no_rule \
  --rule_only \
  --alpha 0.5 \
  --log no_guidance_n50k.txt
```

```bash
# 运行搜索并导出解（示例）
python test_guided_astar_final.py \
  --instance instance_28.txt \
  --model improved_model_best_w010_run2.pth \
  --max_nodes 50000 \
  --use_rule \
  --alpha 0.5 \
  --solution_out outputs/instance_28_solution.json

# 渲染为 GIF
python render_solution_gif.py \
  --input outputs/instance_28_solution.json \
  --output outputs/instance_28_solution.gif \
  --fps 2
```

JSON格式（version=1）关键字段：

- problem.initial / problem.goal：二维数组，字符串块ID
- solution.trajectory：每一帧的状态（包含初始帧）
- solution.actions：动作字符串序列，例如`"(1, 3)"`

## 未来工作方向

### 短期改进

1. **数据增强**

   - 生成更多训练样本
   - 平衡类别分布
   - 覆盖关键状态转换
2. **模型架构优化**

   - 尝试图神经网络
   - 探索更深的Transformer
   - 添加注意力可视化
3. **搜索算法改进**

   - 实现束搜索
   - 添加蒙特卡洛树搜索
   - 优化启发式函数

### 长期研究

1. **迁移学习**

   - 在相关任务上预训练
   - 少样本学习技术
   - 领域自适应
2. **可解释性**

   - 注意力机制分析
   - 决策路径可视化
   - 规则提取
3. **实际应用**

   - 扩展到更复杂的规划问题
   - 实时系统集成
   - 人机协作界面

## 项目结构

```
improved_model/
├── build_dataset.py           # 数据处理管道
├── convert_instance_to_json.py # 实例格式转换工具
├── improved_cnn_model.py      # 模型定义
├── train_improved_model.py    # 训练脚本
├── test_guided_astar_final.py # 测试脚本（含通用规则）
├── improved_model_best.pth    # 最佳模型权重
├── README.md                  # 项目文档
└── no_use/                    # 归档的旧版本和测试文件
```

## 致谢

本项目的成功得益于：

- LLM生成的高质量引导数据
- PyTorch深度学习框架
- A*搜索算法的经典设计
- 迭代开发和持续改进的方法论

## 总结

这个项目展示了如何将现代深度学习技术与经典AI算法结合，解决实际的规划问题。通过识别和解决一系列技术挑战，我们不仅构建了一个工作系统，还获得了关于混合AI系统设计的宝贵见解。最重要的启示是：在数据有限的情况下，领域知识和学习模型的结合可以产生比任一方法单独使用更好的结果。

项目的成功不仅在于最终的技术实现，更在于问题解决过程中的学习和发现。每个遇到的挑战都成为改进系统的机会，每个失败的尝试都提供了有价值的信息。这种迭代、实验和改进的过程是AI系统开发的核心。

---

*最后更新：2025年8月*
*作者：[您的名字]*
*联系方式：[您的邮箱]*
