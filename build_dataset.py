"""
从 Node 风格的 LLM 日志直接构建训练数据集（一步完成）
- 解析日志 -> 提取原始样本 -> 编码为 (N, 22, 15, 6) -> 保存 .npy 与元数据
- 可选：同时导出原始样本 JSON，便于调试或复用
"""
import re
import json
import ast
import argparse
from dataclasses import dataclass, asdict
from typing import Dict, List, Tuple, Optional
import os

import numpy as np

# 复用改进的编码器
from improved_cnn_model import BlocksWorldEncoder


@dataclass
class TrainingSample:
    current_state: Dict[str, List[int]]
    goal_state: Dict[str, List[int]]
    best_action: Optional[Tuple[int, int]] = None
    worst_action: Optional[Tuple[int, int]] = None
    best_reason: Optional[str] = None
    worst_reason: Optional[str] = None
    fix_stack: Optional[str] = None
    current_issue: Optional[str] = None
    priority_task: Optional[str] = None
    node_id: Optional[int] = None


class LLMLogParser:
    """逐行解析 Node 风格日志并按 Node ID 聚合样本"""
    def __init__(self):
        self.node_line_pattern = re.compile(r"^\s*Node\s+(\d+):\s*(.*)$")
        self.state_pattern = re.compile(r"^(Current state|Goal state):\s*({.*})\s*$")
        self.action_pattern = re.compile(r"^LLM suggests\s+(Best|Worst)\s+Action\s+'?\((\d+)\s*,\s*(\d+)\)'?\s*$")
        self.reason_pattern = re.compile(r"^(Best|Worst)\s+Reason:\s*(.+?)\s*$")
        self.fix_stack_pattern = re.compile(r"^Current fix stack:\s*\*?\*?(Stack\d+)\s*$")
        self.issue_pattern = re.compile(r"^Current issue:\s*(.+?)\s+Priority\s*$")
        self.priority_pattern = re.compile(r"^Priority task:\s*(.+?)\s*$")

    def parse_file(self, filepath: str) -> List[TrainingSample]:
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        samples_by_node: Dict[int, TrainingSample] = {}
        last_best_node: Optional[int] = None
        last_worst_node: Optional[int] = None
        current_node: Optional[int] = None

        for raw_line in lines:
            line = raw_line.strip()
            if not line:
                continue

            m_node = self.node_line_pattern.match(line)
            node_prefix_text = None
            if m_node:
                current_node = int(m_node.group(1))
                node_prefix_text = m_node.group(2).strip()
                if current_node not in samples_by_node:
                    samples_by_node[current_node] = TrainingSample(current_state={}, goal_state={}, node_id=current_node)

            target_line = node_prefix_text if node_prefix_text is not None else line

            m = self.fix_stack_pattern.match(target_line)
            if m and current_node is not None:
                samples_by_node[current_node].fix_stack = m.group(1)
                continue

            m = self.issue_pattern.match(target_line)
            if m and current_node is not None:
                samples_by_node[current_node].current_issue = m.group(1).strip()
                continue

            m = self.priority_pattern.match(target_line)
            if m and current_node is not None:
                samples_by_node[current_node].priority_task = m.group(1).strip()
                continue

            m = self.state_pattern.match(target_line)
            if m and current_node is not None:
                state_type, state_str = m.group(1), m.group(2)
                try:
                    state_dict = ast.literal_eval(state_str)
                    state_dict = {k: [int(x) for x in v] for k, v in state_dict.items()}
                    if state_type == "Current state":
                        samples_by_node[current_node].current_state = state_dict
                    else:
                        samples_by_node[current_node].goal_state = state_dict
                except Exception as e:
                    print(f"解析状态失败 (Node {current_node}): {e}")
                continue

            m = self.action_pattern.match(target_line)
            if m and current_node is not None:
                action_type, from_stack, to_stack = m.group(1), int(m.group(2)), int(m.group(3))
                action = (from_stack - 1, to_stack - 1)
                if action_type == "Best":
                    samples_by_node[current_node].best_action = action
                    last_best_node = current_node
                else:
                    samples_by_node[current_node].worst_action = action
                    last_worst_node = current_node
                continue

            m = self.reason_pattern.match(target_line)
            if m:
                reason_type, reason_text = m.group(1), m.group(2).strip()
                if reason_type == "Best" and last_best_node is not None:
                    samples_by_node[last_best_node].best_reason = reason_text
                elif reason_type == "Worst" and last_worst_node is not None:
                    samples_by_node[last_worst_node].worst_reason = reason_text
                continue

        samples = [s for s in samples_by_node.values() if self._is_valid_sample(s)]
        samples.sort(key=lambda s: (s.node_id if s.node_id is not None else 0))
        return samples

    def _is_valid_sample(self, sample: TrainingSample) -> bool:
        if not sample.current_state or not sample.goal_state:
            return False
        if sample.best_action is None and sample.worst_action is None:
            return False
        return True


class DatasetBuilder:
    """将原始样本编码为模型训练数据"""
    def __init__(self, n_blocks: int = 15, n_stacks: int = 5):
        # 使用 n_stacks+1（多一缓冲栈）；动作空间仍按 n_stacks 计算（如 5 栈 -> 20 动作）
        self.encoder = BlocksWorldEncoder(n_blocks=n_blocks, n_stacks=n_stacks + 1)
        self.n_stacks = n_stacks
        self.n_actions = n_stacks * (n_stacks - 1)

    def num_to_letter(self, n: int) -> Optional[str]:
        if isinstance(n, int) and 1 <= n <= self.encoder.n_blocks:
            return chr(ord('A') + n - 1)
        return None

    def normalize_state(self, raw_state: Dict[str, List[int]], ensure_extra_stack: bool = True) -> Dict[str, List[str]]:
        """
        将 {'Stack1':[12,13,...], ...} -> {'stack1':['L','M',...], ..., 'stack6': []}
        保持底->顶顺序，编码器据此计算距顶距离
        """
        norm: Dict[str, List[str]] = {}
        for i in range(1, self.n_stacks + 1):
            key_candidates = [f'Stack{i}', f'stack{i}']
            values: List[int] = []
            for k in key_candidates:
                if k in raw_state:
                    values = raw_state[k]
                    break
            mapped: List[str] = []
            for v in values:
                letter = self.num_to_letter(v)
                if letter is not None:
                    mapped.append(letter)
            norm[f'stack{i}'] = mapped
        if ensure_extra_stack:
            norm[f'stack{self.n_stacks + 1}'] = []
        return norm

    def encode_states_and_successors(self, sample: TrainingSample) -> Tuple[np.ndarray, np.ndarray]:
        # 状态规范化并编码
        cur_state = self.normalize_state(sample.current_state, ensure_extra_stack=True)
        goal_state = self.normalize_state(sample.goal_state, ensure_extra_stack=True)
        goal_matrix = self.encoder.encode_state(goal_state)
        current_matrix = self.encoder.encode_state(cur_state)

        # 生成20个后继（仅在前 self.n_stacks 栈之间转移）
        successors = []
        for from_stack in range(self.n_stacks):
            for to_stack in range(self.n_stacks):
                if from_stack == to_stack:
                    continue
                successor = self.encoder.apply_action(current_matrix, from_stack, to_stack)
                successors.append(successor)

        # 组合为 (22, n_blocks, n_stacks+1)
        n_layers = 2 + len(successors)
        matrix = np.zeros((n_layers, self.encoder.n_blocks, self.encoder.n_stacks), dtype=np.float32)
        matrix[0] = goal_matrix
        matrix[1] = current_matrix
        for i, suc in enumerate(successors):
            matrix[i + 2] = suc

        # 标签：[-1, -1] 默认；有则编码到 0..19
        labels = np.array([-1, -1], dtype=np.int64)
        if sample.best_action is not None:
            fs, ts = sample.best_action
            labels[0] = self.encode_action(fs, ts)
        if sample.worst_action is not None:
            fs, ts = sample.worst_action
            labels[1] = self.encode_action(fs, ts)
        # 若两个标签均为 -1，则该样本对训练无贡献，可以跳过
        if labels[0] == -1 and labels[1] == -1:
            raise ValueError("动作超出可训练动作空间（前 n_stacks），跳过该样本")

        return matrix, labels

    def encode_action(self, from_stack: int, to_stack: int) -> int:
        # 超出前 n_stacks 的动作（例如来自第6栈的动作）在训练集中记为无标签（-1）
        if not (0 <= from_stack < self.n_stacks and 0 <= to_stack < self.n_stacks) or from_stack == to_stack:
            return -1
        # 映射规则与 BlocksWorldEncoder.encode_action 一致，但动作空间仅限前 n_stacks
        if to_stack > from_stack:
            adjusted_to = to_stack - 1
        else:
            adjusted_to = to_stack
        return from_stack * (self.n_stacks - 1) + adjusted_to


def main():
    parser = argparse.ArgumentParser(description="从Node风格日志直接构建训练数据集（一步完成）")
    group = parser.add_mutually_exclusive_group(required=False)
    group.add_argument('--input', type=str, default=None, help='单个输入日志文件路径')
    group.add_argument('--input_dir', type=str, default=None, help='包含多个日志文件的目录（扫描 *.txt）')
    group.add_argument('--inputs', type=str, nargs='*', default=None, help='多个输入日志文件路径')
    parser.add_argument('--output_prefix', type=str, default='dataset', help='输出文件前缀')
    parser.add_argument('--n_blocks', type=int, default=15, help='最大块数')
    parser.add_argument('--n_stacks', type=int, default=5, help='实际栈数（不含缓冲栈）')
    parser.add_argument('--save_raw_json', action='store_true', help='同时保存原始样本 JSON')

    args = parser.parse_args()

    # 收集输入文件列表
    files: List[str] = []
    if args.input:
        files = [args.input]
    elif args.inputs:
        files = [p for p in args.inputs if p]
    elif args.input_dir:
        if not os.path.isdir(args.input_dir):
            print(f"输入目录不存在: {args.input_dir}")
            return
        for name in sorted(os.listdir(args.input_dir)):
            if name.lower().endswith('.txt'):
                files.append(os.path.join(args.input_dir, name))
    else:
        # 默认回退到单文件（兼容旧行为）
        default_path = 'llm_guided_test_log.txt'
        if os.path.exists(default_path):
            files = [default_path]
        else:
            print("未指定输入，且默认日志不存在。请使用 --input / --input_dir / --inputs")
            return

    print("解析日志文件:")
    for f in files:
        print(f"  - {f}")

    parser_obj = LLMLogParser()
    all_samples: List[TrainingSample] = []
    for path in files:
        cur_samples = parser_obj.parse_file(path)
        print(f"  提取 {len(cur_samples)} 条样本自 {path}")
        all_samples.extend(cur_samples)

    print(f"样本总数: {len(all_samples)}")
    if not all_samples:
        print("未提取到样本，退出。")
        return

    if args.save_raw_json:
        raw_path = f"{args.output_prefix}_raw.json"
        with open(raw_path, 'w', encoding='utf-8') as f:
            json.dump([asdict(s) for s in all_samples], f, ensure_ascii=False, indent=2)
        print(f"已保存原始样本: {raw_path}")

    builder = DatasetBuilder(n_blocks=args.n_blocks, n_stacks=args.n_stacks)

    matrices: List[np.ndarray] = []
    labels: List[np.ndarray] = []
    for s in all_samples:
        try:
            m, l = builder.encode_states_and_successors(s)
            matrices.append(m)
            labels.append(l)
        except Exception as e:
            print(f"编码样本失败 (Node {s.node_id}): {e}")
            continue

    if not matrices:
        print("没有可用样本，终止保存。")
        return

    matrices = np.array(matrices)
    labels = np.array(labels)

    np.save(f"{args.output_prefix}_matrices.npy", matrices)
    np.save(f"{args.output_prefix}_labels.npy", labels)

    best_valid = int((labels[:, 0] >= 0).sum())
    worst_valid = int((labels[:, 1] >= 0).sum())

    metadata = {
        'n_samples': int(len(matrices)),
        'matrix_shape': list(matrices.shape),
        'label_shape': list(labels.shape),
        'best_coverage': float(best_valid / len(labels)),
        'worst_coverage': float(worst_valid / len(labels)),
        'n_blocks': args.n_blocks,
        'n_stacks': args.n_stacks + 1,  # 实际编码使用的栈数（含缓冲栈）
        'action_classes': args.n_stacks * (args.n_stacks - 1),
        'encoding': 'single_channel_from_top'
    }
    with open(f"{args.output_prefix}_metadata.json", 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)

    print("\n数据集已生成：")
    print(f"  - {args.output_prefix}_matrices.npy  shape={matrices.shape}")
    print(f"  - {args.output_prefix}_labels.npy    shape={labels.shape}")
    print(f"  - {args.output_prefix}_metadata.json")
    print(f"  - Best标签覆盖: {best_valid}/{len(labels)} ({best_valid/len(labels)*100:.1f}%)")
    print(f"  - Worst标签覆盖: {worst_valid}/{len(labels)} ({worst_valid/len(labels)*100:.1f}%)")


if __name__ == '__main__':
    main()
