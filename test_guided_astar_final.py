#!/usr/bin/env python3
"""
Run A* with ImprovedBlocksWorldCNN guidance plus universal rule on a Blocks World instance.
"""
import os
import re
import ast
import argparse
import sys
import torch
import numpy as np

# Local imports
from improved_cnn_model import ImprovedBlocksWorldCNN, BlocksWorldEncoder

# Local import: self-contained limited A*
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
from limited_astar_guidance_local import LimitedGraphPlanningBlocksWorld


class ImprovedModelGuidanceAdapter:
    """Adapter with universal rule for optimal block placement."""

    def __init__(self, model_path: str, embed_dim: int = 128, n_heads: int = 8, dropout: float = 0.1,
                 n_layers: int = 22, n_blocks: int = 15, n_stacks: int = 6, n_classes: int = 20,
                 use_rule: bool = True, rule_only: bool = False):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = ImprovedBlocksWorldCNN(
            n_layers=n_layers,
            n_blocks=n_blocks,
            n_stacks=n_stacks,
            embed_dim=embed_dim,
            n_heads=n_heads,
            n_transformer_layers=3,
            n_classes=n_classes,
            dropout_rate=dropout,
        )
        state = torch.load(model_path, map_location='cpu')
        self.model.load_state_dict(state)
        self.model.to(self.device)
        self.model.eval()
        self.encoder = BlocksWorldEncoder(n_blocks=n_blocks, n_stacks=n_stacks)
        self.use_rule = use_rule
        self.rule_only = rule_only
        self.rule_applications = 0

    def _to_letters(self, state: dict) -> dict:
        """将 {'Stack1': [12,3,...]} 转换为 {'stack1': ['L','C',...]}，自底->顶顺序保持不变。"""
        def num_to_letter(n: int):
            if isinstance(n, int) and 1 <= n <= self.encoder.n_blocks:
                return chr(ord('A') + n - 1)
            return None
        out = {}
        # 确保 1..n_stacks 都有键
        for i in range(1, self.encoder.n_stacks + 1):
            key_variants = [f'Stack{i}', f'stack{i}']
            vals = []
            for k in key_variants:
                if k in state:
                    vals = state[k]
                    break
            mapped = []
            for v in vals:
                letter = num_to_letter(v)
                if letter is not None:
                    mapped.append(letter)
            out[f'stack{i}'] = mapped
        return out

    def _encode(self, goal: dict, cur: dict) -> np.ndarray:
        goal_m = self.encoder.encode_state(self._to_letters(goal))
        cur_m = self.encoder.encode_state(self._to_letters(cur))
        succs = []
        # 20 canonical successors over first 5 stacks (0..4)
        for fs in range(5):
            for ts in range(5):
                if fs == ts:
                    continue
                succs.append(self.encoder.apply_action(cur_m, fs, ts))
        mat = np.zeros((22, self.encoder.n_blocks, self.encoder.n_stacks), dtype=np.float32)
        mat[0] = goal_m
        mat[1] = cur_m
        for i, s in enumerate(succs):
            mat[2 + i] = s
        return mat

    def _check_universal_rule(self, current_state: dict, goal_state: dict, successors):
        """
        Universal Rule: If a stack has no conflict with goal AND the next required
        block is available at top of another stack, that move is optimal.
        """
        if not self.use_rule:
            return None

        # Check each target stack
        for target_num in range(1, 7):
            target_key = f'Stack{target_num}'
            current_stack = current_state.get(target_key, [])
            goal_stack = goal_state.get(target_key, [])

            if not goal_stack:  # No blocks needed in this stack
                continue

            # Check for conflicts
            has_conflict = False
            for i, block in enumerate(current_stack):
                if i >= len(goal_stack) or block != goal_stack[i]:
                    has_conflict = True
                    break

            if has_conflict:
                continue

            # Find next required block
            next_idx = len(current_stack)
            if next_idx >= len(goal_stack):
                continue  # Stack complete

            next_block = goal_stack[next_idx]

            # Find if this block is at top of another stack
            for source_num in range(1, 7):
                if source_num == target_num:
                    continue
                source_key = f'Stack{source_num}'
                source_stack = current_state.get(source_key, [])

                if source_stack and source_stack[-1] == next_block:
                    # Found the optimal move!
                    action = (source_num, target_num)

                    # Find in successors
                    for i, (_ns, succ_action) in enumerate(successors):
                        if succ_action == action:
                            self.rule_applications += 1
                            return {
                                "best_action": i,
                                "worst_action": (i + 1) % len(successors) if len(successors) > 1 else 0,
                                "best_reason": f"RULE: Move block {next_block} from {source_key} to {target_key}",
                                "worst_reason": "Not optimal by rule",
                                "ranking": [{"i": i, "score": 10000.0, "best_p": 1.0, "worst_p": 0.0}],
                            }
        return None

    def _check_universal_worst_rule(self, current_state: dict, goal_state: dict, successors):
        """
        Worst-Action Rule: If a stack has no conflict with goal (its bottom prefix
        matches the goal prefix), then moving its top block to any other stack is
        a deterministically bad (worst) decision.
        """
        if not self.use_rule:
            return None

        # Iterate over possible target stacks (same convention as best-rule)
        for target_num in range(1, 7):
            target_key = f'Stack{target_num}'
            current_stack = current_state.get(target_key, [])
            goal_stack = goal_state.get(target_key, [])

            if not goal_stack:
                # No blocks are needed in this stack; only "no-conflict" is empty
                # and in that case there is no top block to move away.
                continue

            # Check whether current stack has no conflict with goal prefix
            has_conflict = False
            for i, block in enumerate(current_stack):
                if i >= len(goal_stack) or block != goal_stack[i]:
                    has_conflict = True
                    break
            if has_conflict or not current_stack:
                continue

            # Moving its top to any other stack is considered worst.
            for dest_num in range(1, 7):
                if dest_num == target_num:
                    continue
                action = (target_num, dest_num)
                for i, (_ns, succ_action) in enumerate(successors):
                    if succ_action == action:
                        top_block = current_stack[-1]
                        return {
                            "worst_action": i,
                            "worst_reason": f"RULE: Avoid moving top block {top_block} away from conflict-free {target_key}",
                        }
        return None


    def evaluate_actions(self, current_state: dict, goal_state: dict, planner, successors):
        """Evaluate actions with universal rule first, then model."""

        # Check universal rule first
        rule_result = self._check_universal_rule(current_state, goal_state, successors)
        if rule_result is not None:
            return rule_result

        # Fall back to model evaluation unless rule_only
        if getattr(self, 'rule_only', False):
            # In rule-only mode, also apply worst-rule if available; otherwise neutral (no bias)
            worst_rule = self._check_universal_worst_rule(current_state, goal_state, successors)
            if worst_rule is not None:
                return {
                    "best_action": -1,
                    "worst_action": int(worst_rule.get("worst_action", -1)),
                    "best_reason": "rule-only",
                    "worst_reason": worst_rule.get("worst_reason", "rule-only"),
                    "ranking": [],
                }
            return {
                "best_action": -1,
                "worst_action": -1,
                "best_reason": "rule-only (no-op)",
                "worst_reason": "rule-only (no-op)",
                "ranking": [],
            }

        alpha = getattr(self, 'alpha', 0.3)
        x = torch.tensor(self._encode(goal_state, current_state)[None, ...], dtype=torch.float32, device=self.device)
        with torch.no_grad():
            best_logits, worst_logits = self.model(x)
            best_logits = best_logits[0]
            worst_logits = worst_logits[0]
            best_probs = torch.softmax(best_logits, dim=0)
            worst_probs = torch.softmax(worst_logits, dim=0)

        # Map to legal successors
        cand = []
        for i, (_ns, action) in enumerate(successors):
            if not isinstance(action, tuple) or len(action) != 2:
                continue
            fs1, ts1 = action
            fs = int(fs1) - 1
            ts = int(ts1) - 1
            if fs == ts or fs < 0 or fs >= 5 or ts < 0 or ts >= 5:
                continue
            adjusted_to = ts - 1 if ts > fs else ts
            class_id = fs * 4 + adjusted_to
            cand.append((i, class_id))

        if not cand:
            return {
                "best_action": 0,
                "worst_action": 0,
                "best_reason": "fallback",
                "worst_reason": "fallback",
                "ranking": []
            }

        # Score candidates
        scored = []
        for i, cid in cand:
            b = float(best_probs[int(cid)].item())
            w = float(worst_probs[int(cid)].item())
            score = b - alpha * w
            scored.append((i, score, b, w))

        best_i = max(scored, key=lambda t: t[1])[0]
        worst_i = max(scored, key=lambda t: t[3])[0]

        scored.sort(key=lambda t: t[1], reverse=True)
        ranking = [{"i": int(i), "score": float(score), "best_p": float(bp), "worst_p": float(wp)}
                   for (i, score, bp, wp) in scored]
        # 将最优/最差置信度附加到返回 dict，供动态权重计算参考
        best_conf = max(scored, key=lambda t: t[2])[2] if scored else 0.0
        worst_conf = max(scored, key=lambda t: t[3])[3] if scored else 0.0

        # Before returning, apply the worst-action rule to override worst if applicable
        worst_rule = self._check_universal_worst_rule(current_state, goal_state, successors)
        if worst_rule is not None:
            worst_i = worst_rule.get("worst_action", worst_i)
            worst_reason = worst_rule.get("worst_reason", "model-based")
        else:
            worst_reason = "model-based"

        return {
            "best_action": int(best_i),
            "worst_action": int(worst_i),
            "best_reason": "model-based",
            "worst_reason": worst_reason,
            "ranking": ranking,
            "best_confidence": float(best_conf),
            "worst_confidence": float(worst_conf),
        }


def parse_instance(path: str):
    content = open(path, 'r', encoding='utf-8').read()
    start = ast.literal_eval(re.search(r"Start State:\s*(\{.*\})", content).group(1))
    goal = ast.literal_eval(re.search(r"G_canonical:\s*(\{.*\})", content).group(1))
    fix_order = ast.literal_eval(re.search(r"Fix Order:\s*(\[.*\])", content).group(1))
    return start, goal, fix_order


def main():
    ap = argparse.ArgumentParser(description='Run A* with model + universal rule')
    ap.add_argument('--instance', type=str, default=os.path.join(SCRIPT_DIR, 'instance_28.txt'))
    ap.add_argument('--model', type=str, default=os.path.join(SCRIPT_DIR, 'improved_model_best.pth'))
    ap.add_argument('--max_nodes', type=int, default=20000)
    ap.add_argument('--log', type=str, default=os.path.join(SCRIPT_DIR, 'guided_astar_rule_log.txt'))
    ap.add_argument('--alpha', type=float, default=0.3)
    ap.add_argument('--use_rule', action='store_true', default=True)
    ap.add_argument('--no_rule', dest='use_rule', action='store_false')
    ap.add_argument('--rule_only', action='store_true', help='Use only universal rules; disable model scoring')
    ap.add_argument('--use_dynamic_weights', action='store_true', help='Enable heuristic dynamic weighting (consistency-aware)')
    ap.add_argument('--solution_out', type=str, default=None, help='Path to save solution JSON for GIF rendering')
    args = ap.parse_args()

    if not os.path.exists(args.instance):
        print(f"Instance not found: {args.instance}")
        return
    if not os.path.exists(args.model):
        print(f"Model not found: {args.model}")
        return

    start_state, goal_state, fix_order = parse_instance(args.instance)

    print(f"Running A* search:")
    print(f"  Universal rule: {'ENABLED' if args.use_rule else 'DISABLED'}")
    print(f"  Max nodes: {args.max_nodes}")
    print(f"  Alpha: {args.alpha}")

    planner = LimitedGraphPlanningBlocksWorld(start_state, goal_state, fix_order,
                                               log_file=args.log, max_nodes=args.max_nodes,
                                               use_dynamic_weights=args.use_dynamic_weights, dynamic_k=3)
    adapter = ImprovedModelGuidanceAdapter(args.model, use_rule=args.use_rule, rule_only=args.rule_only)
    adapter.alpha = args.alpha
    # Optionally save solution JSON for GIF rendering
    def export_solution_json(solution_actions, start_state, goal_state, out_path):
        import json
        os.makedirs(os.path.dirname(out_path) or '.', exist_ok=True)
        # Build trajectory by simulating actions from start_state
        stack_names = list(start_state.keys())
        def dict_to_list(stacks_dict):
            return [ [str(x) for x in stacks_dict[k]] for k in stack_names ]
        def apply_action(stacks_dict, act):
            fs, ts = act
            fs_name = stack_names[fs-1]
            ts_name = stack_names[ts-1]
            newd = {k: v.copy() for k, v in stacks_dict.items()}
            block = newd[fs_name].pop()
            newd[ts_name].append(block)
            return newd, f"({fs}, {ts})"
        trajectory = []
        cur = {k: v.copy() for k, v in start_state.items()}
        trajectory.append({"state": dict_to_list(cur), "action": ""})
        actions_str = []
        for act in solution_actions:
            cur, act_str = apply_action(cur, act)
            trajectory.append({"state": dict_to_list(cur), "action": act_str})
            actions_str.append(act_str)
        data = {
            "version": 1,
            "problem": {
                "initial": dict_to_list(start_state),
                "goal": dict_to_list(goal_state),
                "blocks": [str(b) for b in sorted({x for v in start_state.values() for x in v} | {x for v in goal_state.values() for x in v})]
            },
            "solution": {
                "actions": actions_str,
                "trajectory": trajectory
            }
        }
        with open(out_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"Solution JSON saved to {out_path}")


    path, nodes = planner.a_star_search(llm=adapter)

    # Export solution if required
    if path and args.solution_out:
        export_solution_json(path, start_state, goal_state, args.solution_out)

    print(f"\n=== Results ===")
    print(f"Solution found: {'Yes' if path else 'No'}")
    print(f"Path length: {len(path) if path else 'N/A'}")
    print(f"Nodes expanded: {nodes}")
    if args.use_rule:
        print(f"Rule applications: {adapter.rule_applications}")
        if adapter.rule_applications > 0:
            print(f"Rule efficiency: {adapter.rule_applications / nodes * 100:.1f}% of decisions")


if __name__ == '__main__':
    main()
